import { useState, useEffect } from 'react';
import {
  AlertTriangle,
  Plus,
  Search,
  Eye,
  Edit,
  UserPlus,
  ArrowRight,
  Clock,
  CheckCircle,
  RefreshCw,
  FileDown,
  Settings
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useRole } from '../hooks/useRole';
import { useModalContext } from '../contexts/ModalContext';
import Cookies from 'js-cookie';
import {
  InvestigationTicket,
  Incident,
  getTickets,
  getIncidents,
  getSeverityColor,
  getStatusColor,
  getPriorityColor,
  getPriorityLabel,
  formatDate
} from '../services/incidentService';
import CreateTicketForm from '../components/incidents/CreateTicketForm';
import TicketDetailsModal from '../components/incidents/TicketDetailsModal';
import IncidentDetailsModal from '../components/incidents/IncidentDetailsModal';

export default function Incidents() {
  const { user, token } = useAuth();
  const { isAdmin } = useRole();
  const { showAlert } = useModalContext();
  const [activeTab, setActiveTab] = useState<'tickets' | 'incidents'>('tickets');
  const [tickets, setTickets] = useState<InvestigationTicket[]>([]);
  const [incidents, setIncidents] = useState<Incident[]>([]);
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<{[key: string]: any}>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<InvestigationTicket | null>(null);
  const [showTicketDetails, setShowTicketDetails] = useState(false);
  const [selectedIncident, setSelectedIncident] = useState<Incident | null>(null);
  const [showIncidentDetails, setShowIncidentDetails] = useState(false);
  const [ticketModalMode, setTicketModalMode] = useState<'view' | 'edit' | 'assign' | 'convert'>('view');
  const [exportingCSV, setExportingCSV] = useState(false);

  // Load data on component mount
  useEffect(() => {
    console.log('🚀 useEffect triggered with:', { user, isAdmin, token: !!token });
    loadTickets();
    loadIncidents();
    loadUsers();
  }, [user, isAdmin, token]);

  const loadTickets = async () => {
    try {
      setLoading(true);
      const data = await getTickets();
      setTickets(data);
    } catch (error) {
      console.error('Failed to load tickets:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadIncidents = async () => {
    try {
      const data = await getIncidents();
      setIncidents(data);
    } catch (error) {
      console.error('Failed to load incidents:', error);
    }
  };

  const loadUsers = async () => {
    console.log('🔍 loadUsers called');
    console.log('🔍 token:', token);
    console.log('🔍 isAdmin:', isAdmin);
    console.log('🔍 user:', user);

    try {
      if (user) {
        console.log('✅ User exists, setting user info...');
        const userId = user.id || user._id;
        console.log('🆔 User ID found:', userId);

        if (userId) {
          const userMap = {
            [userId]: {
              _id: userId,
              email: user.email,
              username: user.username,
              role: user.role
            }
          };
          setUsers(userMap);
          console.log('✅ User map set:', userMap);
        } else {
          console.error('❌ No user ID found in user object');
        }
      } else {
        console.error('❌ No user object found');
      }
    } catch (error) {
      console.error('💥 Error loading users:', error);
    }
  };



  const handleViewTicket = (ticket: InvestigationTicket) => {
    setSelectedTicket(ticket);
    setTicketModalMode('view');
    setShowTicketDetails(true);
  };

  const handleEditTicket = (ticket: InvestigationTicket) => {
    setSelectedTicket(ticket);
    setTicketModalMode('edit');
    setShowTicketDetails(true);
  };

  const handleAssignTicket = (ticket: InvestigationTicket) => {
    setSelectedTicket(ticket);
    setTicketModalMode('assign');
    setShowTicketDetails(true);
  };

  const handleConvertTicket = (ticket: InvestigationTicket) => {
    setSelectedTicket(ticket);
    setTicketModalMode('convert');
    setShowTicketDetails(true);
  };

  const handleTicketUpdated = () => {
    loadTickets();
    loadIncidents();
  };

  const handleViewIncident = (incident: Incident) => {
    setSelectedIncident(incident);
    setShowIncidentDetails(true);
  };

  const handleEditIncident = (incident: Incident) => {
    setSelectedIncident(incident);
    setShowIncidentDetails(true);
    // The modal will handle edit mode internally
  };

  const handleAssignIncident = (incident: Incident) => {
    // Open the incident details modal in edit mode for assignment
    setSelectedIncident(incident);
    setShowIncidentDetails(true);
  };

  const handleIncidentUpdated = () => {
    loadIncidents();
    setSelectedIncident(null);
    setShowIncidentDetails(false);
  };

  // Function to export all incidents and tickets to CSV
  const handleExportIncidentsCSV = async () => {
    try {
      setExportingCSV(true);
      console.log('📊 Exporting all incidents and tickets to CSV');

      const token = Cookies.get('token');
      if (!token) {
        showAlert({
          type: 'error',
          title: 'Authentication required',
          message: 'Please log in to export data'
        });
        return;
      }

      const response = await fetch(`http://localhost:5000/api/export/incidents/csv`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // Generate filename
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        link.download = `PICA_Incidents_Export_${timestamp}.csv`;

        // Trigger download
        document.body.appendChild(link);
        link.click();

        // Cleanup
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        console.log('✅ Incidents CSV exported successfully');

        showAlert({
          type: 'success',
          title: 'Export successful',
          message: 'All incidents and tickets exported successfully to CSV.'
        });
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to export incidents CSV:', response.status, errorData);
        showAlert({
          type: 'error',
          title: 'Export error',
          message: 'CSV export error: ' + (errorData.message || 'Unknown error')
        });
      }
    } catch (error) {
      console.error('❌ Error exporting incidents CSV:', error);
      showAlert({
        type: 'error',
        title: 'Export error',
        message: 'An error occurred during CSV export'
      });
    } finally {
      setExportingCSV(false);
    }
  };

  // Filter tickets based on search and filters
  const filteredTickets = tickets.filter(ticket => {
    const title = ticket.short_description || ticket.title || '';
    const matchesSearch = title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.ticket_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (ticket.category && ticket.category.toLowerCase().includes(searchTerm.toLowerCase()));

    // Handle both old severity and new impact/priority filtering
    const ticketSeverity = ticket.severity || ticket.impact?.toLowerCase();
    const matchesSeverity = severityFilter === 'all' || ticketSeverity === severityFilter;
    const matchesStatus = statusFilter === 'all' || ticket.status.toLowerCase() === statusFilter.toLowerCase();
    return matchesSearch && matchesSeverity && matchesStatus;
  });

  // Filter incidents based on search and filters
  const filteredIncidents = incidents.filter(incident => {
    const matchesSearch = incident.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         incident.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         incident.incident_id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSeverity = severityFilter === 'all' || incident.severity === severityFilter;
    const matchesStatus = statusFilter === 'all' || incident.status === statusFilter;
    return matchesSearch && matchesSeverity && matchesStatus;
  });

  const renderTabButton = (tab: 'tickets' | 'incidents', label: string, icon: any) => {
    const Icon = icon;
    return (
      <button
        onClick={() => setActiveTab(tab)}
        className={`px-6 py-3 rounded-lg font-medium transition-all ${
          activeTab === tab
            ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
            : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
        }`}
      >
        <Icon size={20} className="inline mr-2" />
        {label}
      </button>
    );
  };

  const renderFilters = () => (
    <div className="flex flex-wrap gap-4 mb-8">
      <div className="flex-1 min-w-64">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search tickets, incidents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
          />
        </div>
      </div>

      <select
        value={severityFilter}
        onChange={(e) => setSeverityFilter(e.target.value)}
        className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
      >
        <option value="all">All Severities</option>
        <option value="critical">Critical</option>
        <option value="high">High</option>
        <option value="medium">Medium</option>
        <option value="low">Low</option>
      </select>

      <select
        value={statusFilter}
        onChange={(e) => setStatusFilter(e.target.value)}
        className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
      >
        <option value="all">All Statuses</option>
        <option value="open">Open</option>
        <option value="in_progress">In Progress</option>
        <option value="converted_to_incident">Converted</option>
        <option value="resolved">Resolved</option>
        <option value="closed">Closed</option>
      </select>

      <button
        onClick={() => {
          loadTickets();
          loadIncidents();
        }}
        className="flex items-center space-x-2 px-4 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
      >
        <RefreshCw size={20} />
        <span>Refresh</span>
      </button>
    </div>
  );





  const renderTicketRow = (ticket: InvestigationTicket) => {
    const title = ticket.short_description || ticket.title || 'No title';
    const severity = ticket.severity || ticket.impact?.toLowerCase();

    // Fallback: si l'utilisateur actuel est le créateur du ticket, utiliser ses infos
    let createdBy = users[ticket.user_id]?.email || users[ticket.user_id]?.username;

    if (!createdBy && user && (user.id === ticket.user_id || user._id === ticket.user_id)) {
      createdBy = user.email || user.username;
    }

    createdBy = createdBy || ticket.user_id || '-';

    return (
      <div key={ticket.id} className="bg-gradient-to-r from-gray-800/40 to-gray-900/40 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 hover:border-purple-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-2xl">
        {/* Header Section */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-lg font-semibold text-white">{title}</h3>
              {ticket.category && (
                <span className="px-3 py-1 bg-gradient-to-r from-gray-700 to-gray-800 text-gray-300 text-xs rounded-lg border border-gray-600/30">
                  {ticket.category}
                </span>
              )}
            </div>
            <p className="text-gray-400 text-sm mb-2 font-mono">{ticket.ticket_number}</p>
            <p className="text-gray-300 text-sm line-clamp-2">{ticket.description}</p>
          </div>

          {/* Status Badges */}
          <div className="flex flex-col items-end space-y-2 ml-4">
            {severity && (
              <span className={`px-3 py-1 rounded-xl text-xs font-medium ${getSeverityColor(severity)}`}>
                {severity.toUpperCase()}
              </span>
            )}
            <span className={`px-3 py-1 rounded-xl text-xs font-medium ${getStatusColor(ticket.status)}`}>
              {ticket.status.replace('_', ' ').toUpperCase()}
            </span>
          </div>
        </div>

        {/* Info Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 p-4 bg-gray-800/30 rounded-xl border border-gray-700/30">
          <div className="text-center">
            <p className="text-gray-400 text-xs uppercase tracking-wide mb-1">Created</p>
            <p className="text-white text-sm font-medium">{formatDate(ticket.created_at)}</p>
          </div>
          <div className="text-center">
            <p className="text-gray-400 text-xs uppercase tracking-wide mb-1">Created By</p>
            <p className="text-cyan-400 text-sm font-medium truncate" title={createdBy}>{createdBy}</p>
          </div>
          <div className="text-center">
            <p className="text-gray-400 text-xs uppercase tracking-wide mb-1">Assigned To</p>
            <p className="text-green-400 text-sm font-medium">{ticket.assigned_to || 'Unassigned'}</p>
          </div>
          <div className="text-center">
            <p className="text-gray-400 text-xs uppercase tracking-wide mb-1">Type</p>
            <p className="text-purple-400 text-sm font-medium">{ticket.category || 'General'}</p>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-700/30">
          <div className="flex items-center space-x-4 text-sm text-gray-400">
            <span className="flex items-center">
              <CheckCircle size={16} className="mr-1" />
              ID: {ticket.ticket_number}
            </span>
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => handleViewTicket(ticket)}
              className="p-2 text-gray-400 hover:text-blue-400 hover:bg-blue-900/30 rounded-xl transition-all duration-200 transform hover:scale-110"
              title="View Details"
            >
              <Eye size={16} />
            </button>

            {(isAdmin || ticket.user_id === user?.id) && (
              <button
                onClick={() => handleEditTicket(ticket)}
                className="p-2 text-gray-400 hover:text-purple-400 hover:bg-purple-900/30 rounded-xl transition-all duration-200 transform hover:scale-110"
                title="Edit Ticket"
              >
                <Edit size={16} />
              </button>
            )}

            {isAdmin && (
              <button
                onClick={() => handleAssignTicket(ticket)}
                className="p-2 text-gray-400 hover:text-green-400 hover:bg-green-900/30 rounded-xl transition-all duration-200 transform hover:scale-110"
                title="Assign Ticket"
              >
                <UserPlus size={16} />
              </button>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderIncidentRow = (incident: Incident) => (
    <tr key={incident.id} className="border-b border-gray-700/30 hover:bg-gray-800/30 transition-all duration-200">
      <td className="py-4 px-6 border-r border-gray-700/30">
        <div>
          <h3 className="text-white font-medium mb-1">{incident.title}</h3>
          <p className="text-gray-400 text-sm">({incident.description})</p>
        </div>
      </td>
      <td className="py-4 px-6 border-r border-gray-700/30">
        <span className={`px-3 py-1 rounded-xl text-xs font-medium ${getSeverityColor(incident.severity)}`}>
          {incident.severity}
        </span>
      </td>
      <td className="py-4 px-6 border-r border-gray-700/30">
        <span className={`px-3 py-1 rounded-xl text-xs font-medium ${getStatusColor(incident.status)}`}>
          {incident.status.replace('_', ' ')}
        </span>
      </td>
      <td className="py-4 px-6 text-gray-300 text-sm border-r border-gray-700/30">
        {formatDate(incident.created_at)}
      </td>
      <td className="py-4 px-6 text-gray-300 text-sm border-r border-gray-700/30">
        {incident.assigned_to || '-'}
      </td>
      <td className="py-4 px-6 text-gray-300 text-sm border-r border-gray-700/30">
        {incident.escalated ? 'Escalated' : incident.false_positive ? 'False Positive' : 'Security Incident'}
      </td>
      <td className="py-4 px-6">
        <div className="flex space-x-2">
          <button
            onClick={() => handleViewIncident(incident)}
            className="p-2 text-gray-400 hover:text-blue-400 hover:bg-blue-900/30 rounded-lg transition-all duration-200"
            title="View Details"
          >
            <Eye size={16} />
          </button>
          {isAdmin && (
            <>
              <button
                onClick={() => handleEditIncident(incident)}
                className="p-2 text-gray-400 hover:text-purple-400 hover:bg-purple-900/30 rounded-lg transition-all duration-200"
                title="Edit"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={() => handleAssignIncident(incident)}
                className="p-2 text-gray-400 hover:text-green-400 hover:bg-green-900/30 rounded-lg transition-all duration-200"
                title="Assign"
              >
                <UserPlus size={16} />
              </button>
            </>
          )}
        </div>
      </td>
    </tr>
  );

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-2xl">
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
            <AlertTriangle className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">Incident Management</h1>
            <p className="text-gray-300">Manage security incidents and investigation tickets</p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Export CSV Button - Always visible for authenticated users */}
            <button
              onClick={handleExportIncidentsCSV}
              disabled={exportingCSV}
              className="flex items-center space-x-2 px-4 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
              title="Export all incidents and tickets to CSV"
            >
              {exportingCSV ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Exporting...</span>
                </>
              ) : (
                <>
                  <FileDown className="w-4 h-4" />
                  <span>Export All CSV</span>
                </>
              )}
            </button>
          </div>

          <button
            onClick={() => setShowCreateForm(true)}
            className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
          >
            <Plus size={20} />
            <span>Create Ticket</span>
          </button>
        </div>

        {/* Navigation Tabs */}
        <div className="flex justify-center mt-8">
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-1 border border-gray-700/50">
            {renderTabButton('tickets', 'Investigation Tickets', AlertTriangle)}
            {renderTabButton('incidents', 'Security Incidents', CheckCircle)}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl shadow-2xl">
        {activeTab === 'tickets' && (
          <div className="p-8">
            {/* Header with Create Button */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white">Investigation Tickets</h2>
              <button
                onClick={() => setShowCreateForm(true)}
                className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                <Plus size={20} />
                <span>Create Ticket</span>
              </button>
            </div>

            {/* Search and Filters */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search tickets..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <select
                  value={severityFilter}
                  onChange={(e) => setSeverityFilter(e.target.value)}
                  className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
                >
                  <option value="all">All Severities</option>
                  <option value="critical">Critical</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>

                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
                >
                  <option value="all">All Statuses</option>
                  <option value="open">Open</option>
                  <option value="in_progress">In Progress</option>
                  <option value="converted_to_incident">Converted</option>
                  <option value="resolved">Resolved</option>
                  <option value="closed">Closed</option>
                </select>

                <button className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-gray-300 hover:text-white transition-all duration-200">
                  Filters
                </button>

                <button className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-gray-300 hover:text-white transition-all duration-200">
                  Saved
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'incidents' && (
          <div className="p-8">
            {/* Header with Create Button */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white">Security Incidents</h2>
              <button
                onClick={() => setShowCreateForm(true)}
                className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                <Plus size={20} />
                <span>Create Incident</span>
              </button>
            </div>

            {/* Search and Filters */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search incidents..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <select
                  value={severityFilter}
                  onChange={(e) => setSeverityFilter(e.target.value)}
                  className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
                >
                  <option value="all">All Severities</option>
                  <option value="critical">Critical</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>

                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
                >
                  <option value="all">All Statuses</option>
                  <option value="open">Open</option>
                  <option value="in_progress">In Progress</option>
                  <option value="resolved">Resolved</option>
                  <option value="closed">Closed</option>
                </select>

                <button className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-gray-300 hover:text-white transition-all duration-200">
                  Filters
                </button>

                <button className="px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-gray-300 hover:text-white transition-all duration-200">
                  Saved
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'tickets' && (
          <div className="px-8 pb-8">
            {/* Tickets Grid */}
            <div className="space-y-4">
              {loading ? (
                <div className="text-center py-12">
                  <RefreshCw className="animate-spin mx-auto mb-4 text-purple-400" size={48} />
                  <p className="text-gray-400">Loading tickets...</p>
                </div>
              ) : filteredTickets.length > 0 ? (
                filteredTickets.map(renderTicketRow)
              ) : (
                <div className="text-center py-12">
                  <AlertTriangle className="mx-auto mb-4 text-gray-400" size={48} />
                  <div>
                    <p className="text-gray-400 text-lg mb-2">
                      {isAdmin ? 'No tickets found' : 'You haven\'t created any tickets yet'}
                    </p>
                    <p className="text-gray-500 text-sm">
                      {isAdmin
                        ? 'Tickets will appear here when users submit them'
                        : 'Create your first ticket to report an issue or request assistance'
                      }
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'incidents' && (
          <div className="px-8 pb-8">
            {/* Incidents Table */}
            <div className="bg-gray-800/30 rounded-xl border border-gray-700/50 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-800/50 border-b border-gray-700/50">
                      <th className="text-left py-4 px-6 text-gray-300 font-medium border-r border-gray-700/30">Title</th>
                      <th className="text-left py-4 px-6 text-gray-300 font-medium border-r border-gray-700/30">Severity</th>
                      <th className="text-left py-4 px-6 text-gray-300 font-medium border-r border-gray-700/30">Status</th>
                      <th className="text-left py-4 px-6 text-gray-300 font-medium border-r border-gray-700/30">Created</th>
                      <th className="text-left py-4 px-6 text-gray-300 font-medium border-r border-gray-700/30">Assigned To</th>
                      <th className="text-left py-4 px-6 text-gray-300 font-medium border-r border-gray-700/30">Type</th>
                      <th className="text-left py-4 px-6 text-gray-300 font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredIncidents.length > 0 ? (
                      filteredIncidents.map(renderIncidentRow)
                    ) : (
                      <tr>
                        <td colSpan={7} className="text-center py-12 border-r border-gray-700/30">
                          <CheckCircle className="mx-auto mb-4 text-gray-400" size={48} />
                          <p className="text-gray-400">No incidents found</p>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Create Ticket Form Modal */}
      <CreateTicketForm
        isOpen={showCreateForm}
        onClose={() => setShowCreateForm(false)}
        onTicketCreated={() => {
          loadTickets();
          setShowCreateForm(false);
        }}
      />

      {/* Ticket Details Modal */}
      <TicketDetailsModal
        ticket={selectedTicket}
        isOpen={showTicketDetails}
        onClose={() => {
          setShowTicketDetails(false);
          setSelectedTicket(null);
          setTicketModalMode('view');
        }}
        onTicketUpdated={handleTicketUpdated}
        initialMode={ticketModalMode}
        currentUserId={user?.id}
      />

      {/* Incident Details Modal */}
      {selectedIncident && (
        <IncidentDetailsModal
          incident={selectedIncident}
          isOpen={showIncidentDetails}
          onClose={() => {
            setShowIncidentDetails(false);
            setSelectedIncident(null);
          }}
          onIncidentUpdated={handleIncidentUpdated}
        />
      )}
    </div>
  );
}
